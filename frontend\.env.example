# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SOCKET_URL=http://localhost:5000

# Payment Gateway
REACT_APP_STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key

# Google Maps (for location services)
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# External APIs
REACT_APP_AMADEUS_API_KEY=your-amadeus-api-key

# App Configuration
REACT_APP_NAME=AI Travel Booking
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Features Flags
REACT_APP_ENABLE_AI_CHAT=true
REACT_APP_ENABLE_PRICE_PREDICTION=true
REACT_APP_ENABLE_RECOMMENDATIONS=true

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=your-ga-id
REACT_APP_HOTJAR_ID=your-hotjar-id

# Social Login
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_FACEBOOK_APP_ID=your-facebook-app-id
