{"name": "@types/history", "version": "4.7.11", "description": "TypeScript definitions for history", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/history", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rokoroku", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/danielnixon", "githubUsername": "danie<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/history"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "141516ba36ab9f2b221dc957cba4ac21d9a06776c05786e6773c5581f8cf7455", "typeScriptVersion": "3.8"}