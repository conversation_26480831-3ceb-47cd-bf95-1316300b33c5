import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store/store';
import { clearCredentials } from '../store/slices/authSlice';
import toast from 'react-hot-toast';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error) => {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      store.dispatch(clearCredentials());
      toast.error('Session expired. Please login again.');
      window.location.href = '/login';
    }
    
    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: { email: string; password: string }) =>
    api.post('/auth/login', credentials),
  
  register: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone?: string;
  }) => api.post('/auth/register', userData),
  
  logout: () => api.post('/auth/logout'),
  
  getCurrentUser: () => api.get('/auth/me'),
  
  updateProfile: (userData: any) => api.put('/auth/update-details', userData),
  
  updatePassword: (passwordData: {
    currentPassword: string;
    newPassword: string;
  }) => api.put('/auth/update-password', passwordData),
  
  forgotPassword: (email: string) => api.post('/auth/forgot-password', { email }),
  
  resetPassword: (token: string, password: string) =>
    api.put(`/auth/reset-password/${token}`, { password }),
  
  verifyEmail: (token: string) => api.get(`/auth/verify-email/${token}`),
  
  resendVerification: (email: string) =>
    api.post('/auth/resend-verification', { email }),
};

// Flight API
export const flightAPI = {
  searchFlights: (params: any) => api.get('/flights/search', { params }),
  
  getFlightDetails: (flightId: string) => api.get(`/flights/${flightId}`),
  
  getFlightsByRoute: (from: string, to: string) =>
    api.get(`/flights/route/${from}/${to}`),
  
  getPopularDestinations: () => api.get('/flights/popular-destinations'),
  
  getFlightPriceHistory: (flightId: string) =>
    api.get(`/flights/${flightId}/price-history`),
  
  getFlightAvailability: (flightId: string) =>
    api.get(`/flights/${flightId}/availability`),
  
  getFlightRecommendations: () => api.get('/flights/recommendations/personalized'),
  
  addToWishlist: (flightId: string) => api.post(`/flights/${flightId}/wishlist`),
  
  removeFromWishlist: (flightId: string) =>
    api.delete(`/flights/${flightId}/wishlist`),
};

// Hotel API
export const hotelAPI = {
  searchHotels: (params: any) => api.get('/hotels/search', { params }),
  
  getHotelDetails: (hotelId: string) => api.get(`/hotels/${hotelId}`),
  
  getHotelsByCity: (city: string) => api.get(`/hotels/city/${city}`),
  
  getPopularHotels: () => api.get('/hotels/popular'),
  
  getHotelReviews: (hotelId: string) => api.get(`/hotels/${hotelId}/reviews`),
  
  addHotelReview: (hotelId: string, review: any) =>
    api.post(`/hotels/${hotelId}/reviews`, review),
  
  getHotelAvailability: (hotelId: string) =>
    api.get(`/hotels/${hotelId}/availability`),
  
  getHotelRecommendations: () => api.get('/hotels/recommendations/personalized'),
  
  getNearbyAttractions: (hotelId: string) =>
    api.get(`/hotels/${hotelId}/nearby-attractions`),
};

// Booking API
export const bookingAPI = {
  createFlightBooking: (bookingData: any) =>
    api.post('/bookings/flight', bookingData),
  
  createHotelBooking: (bookingData: any) =>
    api.post('/bookings/hotel', bookingData),
  
  createPackageBooking: (bookingData: any) =>
    api.post('/bookings/package', bookingData),
  
  getBookings: () => api.get('/bookings'),
  
  getBookingDetails: (bookingId: string) => api.get(`/bookings/${bookingId}`),
  
  updateBooking: (bookingId: string, updateData: any) =>
    api.put(`/bookings/${bookingId}`, updateData),
  
  cancelBooking: (bookingId: string, reason?: string) =>
    api.delete(`/bookings/${bookingId}`, { data: { reason } }),
  
  processPayment: (bookingId: string, paymentData: any) =>
    api.post(`/bookings/${bookingId}/payment`, paymentData),
  
  getBookingHistory: () => api.get('/bookings/history'),
  
  downloadTicket: (bookingId: string) => api.get(`/bookings/${bookingId}/ticket`),
  
  sendBookingConfirmation: (bookingId: string) =>
    api.post(`/bookings/${bookingId}/send-confirmation`),
};

// AI API
export const aiAPI = {
  getChatbotResponse: (message: string, context?: any) =>
    api.post('/ai/chat', { message, context }),
  
  getPersonalizedRecommendations: (params?: any) =>
    api.get('/ai/recommendations', { params }),
  
  getPricePrediction: (params: any) => api.get('/ai/price-prediction', { params }),
  
  getSmartSearch: (query: string, context?: any) =>
    api.get('/ai/smart-search', { params: { query, context } }),
  
  getTravelInsights: (params?: any) => api.get('/ai/insights', { params }),
  
  updateUserPreferences: (preferences: any) =>
    api.put('/ai/preferences', preferences),
  
  getOptimalBookingTime: () => api.get('/ai/optimal-booking-time'),
  
  getDestinationRecommendations: () => api.get('/ai/destination-recommendations'),
  
  analyzeTravelPattern: () => api.get('/ai/travel-pattern-analysis'),
};

// User API
export const userAPI = {
  getUserProfile: () => api.get('/users/profile'),
  
  updateUserProfile: (profileData: any) => api.put('/users/profile', profileData),
  
  uploadAvatar: (formData: FormData) =>
    api.post('/users/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    }),
  
  deleteAvatar: () => api.delete('/users/avatar'),
  
  getUserBookings: () => api.get('/users/bookings'),
  
  getUserWishlist: () => api.get('/users/wishlist'),
  
  addToWishlist: (itemData: any) => api.post('/users/wishlist', itemData),
  
  removeFromWishlist: (itemId: string) => api.delete(`/users/wishlist/${itemId}`),
  
  getUserPreferences: () => api.get('/users/preferences'),
  
  updateUserPreferences: (preferences: any) =>
    api.put('/users/preferences', preferences),
  
  getUserTravelHistory: () => api.get('/users/travel-history'),
  
  getUserNotifications: () => api.get('/users/notifications'),
  
  markNotificationAsRead: (notificationId: string) =>
    api.put(`/users/notifications/${notificationId}/read`),
  
  updateNotificationSettings: (settings: any) =>
    api.put('/users/notification-settings', settings),
  
  deleteUserAccount: () => api.delete('/users/account'),
};

// Admin API
export const adminAPI = {
  getDashboardStats: () => api.get('/admin/dashboard'),
  
  getAllUsers: (params?: any) => api.get('/admin/users', { params }),
  
  getUserDetails: (userId: string) => api.get(`/admin/users/${userId}`),
  
  updateUserStatus: (userId: string, status: string, reason?: string) =>
    api.put(`/admin/users/${userId}/status`, { status, reason }),
  
  deleteUser: (userId: string) => api.delete(`/admin/users/${userId}`),
  
  getAllBookings: (params?: any) => api.get('/admin/bookings', { params }),
  
  getBookingDetails: (bookingId: string) => api.get(`/admin/bookings/${bookingId}`),
  
  updateBookingStatus: (bookingId: string, status: string, reason?: string) =>
    api.put(`/admin/bookings/${bookingId}/status`, { status, reason }),
  
  getAllFlights: (params?: any) => api.get('/admin/flights', { params }),
  
  createFlight: (flightData: any) => api.post('/admin/flights', flightData),
  
  updateFlight: (flightId: string, flightData: any) =>
    api.put(`/admin/flights/${flightId}`, flightData),
  
  deleteFlight: (flightId: string) => api.delete(`/admin/flights/${flightId}`),
  
  getAllHotels: (params?: any) => api.get('/admin/hotels', { params }),
  
  createHotel: (hotelData: any) => api.post('/admin/hotels', hotelData),
  
  updateHotel: (hotelId: string, hotelData: any) =>
    api.put(`/admin/hotels/${hotelId}`, hotelData),
  
  deleteHotel: (hotelId: string) => api.delete(`/admin/hotels/${hotelId}`),
  
  getRevenueAnalytics: (params?: any) => api.get('/admin/analytics/revenue', { params }),
  
  getUserAnalytics: (params?: any) => api.get('/admin/analytics/users', { params }),
  
  getBookingAnalytics: (params?: any) => api.get('/admin/analytics/bookings', { params }),
  
  getSystemLogs: (params?: any) => api.get('/admin/logs', { params }),
  
  exportData: (params: any) => api.get('/admin/export', { params }),
  
  bulkOperations: (operationData: any) => api.post('/admin/bulk-operations', operationData),
};

export default api;
