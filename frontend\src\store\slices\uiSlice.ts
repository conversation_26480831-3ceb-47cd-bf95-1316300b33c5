import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  searchFiltersOpen: boolean;
  chatbotOpen: boolean;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
    read: boolean;
  }>;
  loading: {
    global: boolean;
    search: boolean;
    booking: boolean;
    payment: boolean;
  };
  modals: {
    login: boolean;
    register: boolean;
    flightDetails: boolean;
    hotelDetails: boolean;
    bookingConfirmation: boolean;
    paymentModal: boolean;
  };
  breadcrumbs: Array<{
    label: string;
    path: string;
  }>;
  pageTitle: string;
  searchHistory: Array<{
    type: 'flight' | 'hotel';
    query: string;
    timestamp: number;
  }>;
}

const initialState: UIState = {
  theme: 'light',
  sidebarOpen: false,
  mobileMenuOpen: false,
  searchFiltersOpen: false,
  chatbotOpen: false,
  notifications: [],
  loading: {
    global: false,
    search: false,
    booking: false,
    payment: false,
  },
  modals: {
    login: false,
    register: false,
    flightDetails: false,
    hotelDetails: false,
    bookingConfirmation: false,
    paymentModal: false,
  },
  breadcrumbs: [],
  pageTitle: '',
  searchHistory: [],
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.mobileMenuOpen = action.payload;
    },
    toggleSearchFilters: (state) => {
      state.searchFiltersOpen = !state.searchFiltersOpen;
    },
    setSearchFiltersOpen: (state, action: PayloadAction<boolean>) => {
      state.searchFiltersOpen = action.payload;
    },
    toggleChatbot: (state) => {
      state.chatbotOpen = !state.chatbotOpen;
    },
    setChatbotOpen: (state, action: PayloadAction<boolean>) => {
      state.chatbotOpen = action.payload;
    },
    addNotification: (state, action: PayloadAction<Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: Date.now(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // Keep only last 50 notifications
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    setLoading: (state, action: PayloadAction<{ key: keyof UIState['loading']; value: boolean }>) => {
      state.loading[action.payload.key] = action.payload.value;
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    openModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = true;
    },
    closeModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = false;
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key as keyof UIState['modals']] = false;
      });
    },
    setBreadcrumbs: (state, action: PayloadAction<UIState['breadcrumbs']>) => {
      state.breadcrumbs = action.payload;
    },
    addBreadcrumb: (state, action: PayloadAction<{ label: string; path: string }>) => {
      state.breadcrumbs.push(action.payload);
    },
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
      document.title = action.payload ? `${action.payload} - AI Travel Booking` : 'AI Travel Booking';
    },
    addToSearchHistory: (state, action: PayloadAction<{ type: 'flight' | 'hotel'; query: string }>) => {
      const searchItem = {
        ...action.payload,
        timestamp: Date.now(),
      };
      
      // Remove duplicate if exists
      state.searchHistory = state.searchHistory.filter(
        item => !(item.type === searchItem.type && item.query === searchItem.query)
      );
      
      // Add to beginning
      state.searchHistory.unshift(searchItem);
      
      // Keep only last 20 searches
      if (state.searchHistory.length > 20) {
        state.searchHistory = state.searchHistory.slice(0, 20);
      }
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
  },
});

export const {
  toggleTheme,
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  toggleMobileMenu,
  setMobileMenuOpen,
  toggleSearchFilters,
  setSearchFiltersOpen,
  toggleChatbot,
  setChatbotOpen,
  addNotification,
  markNotificationAsRead,
  removeNotification,
  clearNotifications,
  setLoading,
  setGlobalLoading,
  openModal,
  closeModal,
  closeAllModals,
  setBreadcrumbs,
  addBreadcrumb,
  setPageTitle,
  addToSearchHistory,
  clearSearchHistory,
} = uiSlice.actions;

export default uiSlice.reducer;
