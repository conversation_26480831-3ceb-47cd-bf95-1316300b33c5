import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { flightAPI } from '../../services/api';

export interface Flight {
  id: string;
  flightNumber: string;
  airline: {
    code: string;
    name: string;
    logo?: string;
  };
  aircraft: string;
  departure: {
    airport: {
      code: string;
      name: string;
      city: string;
      country: string;
    };
    dateTime: string;
    timezone?: string;
  };
  arrival: {
    airport: {
      code: string;
      name: string;
      city: string;
      country: string;
    };
    dateTime: string;
    timezone?: string;
  };
  duration: {
    hours: number;
    minutes: number;
    total: number;
  };
  stops: Array<{
    airport: {
      code: string;
      name: string;
      city: string;
    };
    duration: number;
  }>;
  classes: {
    economy?: {
      available: number;
      price: {
        base: number;
        taxes: number;
        total: number;
      };
    };
    business?: {
      available: number;
      price: {
        base: number;
        taxes: number;
        total: number;
      };
    };
    first?: {
      available: number;
      price: {
        base: number;
        taxes: number;
        total: number;
      };
    };
  };
  amenities: string[];
  status: string;
}

export interface FlightSearchParams {
  from: string;
  to: string;
  departDate: string;
  returnDate?: string;
  passengers: number;
  class: 'economy' | 'business' | 'first';
  tripType: 'one-way' | 'round-trip';
}

interface FlightState {
  flights: Flight[];
  selectedFlight: Flight | null;
  searchParams: FlightSearchParams | null;
  searchResults: {
    flights: Flight[];
    total: number;
    page: number;
    totalPages: number;
  };
  popularDestinations: Array<{
    city: string;
    country: string;
    code: string;
    image: string;
    price: number;
  }>;
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  filters: {
    priceRange: [number, number];
    airlines: string[];
    stops: string[];
    departureTime: string[];
    duration: [number, number];
  };
  sortBy: 'price' | 'duration' | 'departure' | 'arrival';
  sortOrder: 'asc' | 'desc';
}

const initialState: FlightState = {
  flights: [],
  selectedFlight: null,
  searchParams: null,
  searchResults: {
    flights: [],
    total: 0,
    page: 1,
    totalPages: 0,
  },
  popularDestinations: [],
  isLoading: false,
  isSearching: false,
  error: null,
  filters: {
    priceRange: [0, 10000],
    airlines: [],
    stops: [],
    departureTime: [],
    duration: [0, 24],
  },
  sortBy: 'price',
  sortOrder: 'asc',
};

// Async thunks
export const searchFlights = createAsyncThunk(
  'flights/searchFlights',
  async (searchParams: FlightSearchParams & { page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await flightAPI.searchFlights(searchParams);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Flight search failed');
    }
  }
);

export const getFlightDetails = createAsyncThunk(
  'flights/getFlightDetails',
  async (flightId: string, { rejectWithValue }) => {
    try {
      const response = await flightAPI.getFlightDetails(flightId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get flight details');
    }
  }
);

export const getPopularDestinations = createAsyncThunk(
  'flights/getPopularDestinations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await flightAPI.getPopularDestinations();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get popular destinations');
    }
  }
);

const flightSlice = createSlice({
  name: 'flights',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchParams: (state, action: PayloadAction<FlightSearchParams>) => {
      state.searchParams = action.payload;
    },
    setSelectedFlight: (state, action: PayloadAction<Flight | null>) => {
      state.selectedFlight = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<FlightState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSorting: (state, action: PayloadAction<{ sortBy: FlightState['sortBy']; sortOrder: FlightState['sortOrder'] }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearSearchResults: (state) => {
      state.searchResults = {
        flights: [],
        total: 0,
        page: 1,
        totalPages: 0,
      };
      state.searchParams = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Search flights
      .addCase(searchFlights.pending, (state) => {
        state.isSearching = true;
        state.error = null;
      })
      .addCase(searchFlights.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload;
        state.error = null;
      })
      .addCase(searchFlights.rejected, (state, action) => {
        state.isSearching = false;
        state.error = action.payload as string;
      })
      // Get flight details
      .addCase(getFlightDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getFlightDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedFlight = action.payload;
        state.error = null;
      })
      .addCase(getFlightDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get popular destinations
      .addCase(getPopularDestinations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPopularDestinations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.popularDestinations = action.payload;
        state.error = null;
      })
      .addCase(getPopularDestinations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSearchParams,
  setSelectedFlight,
  updateFilters,
  setSorting,
  clearSearchResults,
} = flightSlice.actions;

export default flightSlice.reducer;
