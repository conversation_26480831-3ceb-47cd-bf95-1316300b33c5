import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { bookingAPI } from '../../services/api';

export interface Booking {
  id: string;
  bookingReference: string;
  type: 'flight' | 'hotel' | 'package';
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded';
  user: string;
  createdAt: string;
  updatedAt: string;
  
  // Flight booking details
  flight?: {
    outbound: {
      flight: string;
      class: 'economy' | 'premium_economy' | 'business' | 'first';
      passengers: Array<{
        title: string;
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        gender: string;
        passportNumber?: string;
        passportExpiry?: string;
        nationality: string;
        seatNumber?: string;
        mealPreference?: string;
      }>;
      price: {
        base: number;
        taxes: number;
        fees: number;
        total: number;
      };
    };
    return?: {
      flight: string;
      class: 'economy' | 'premium_economy' | 'business' | 'first';
      passengers: Array<{
        title: string;
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        gender: string;
        passportNumber?: string;
        passportExpiry?: string;
        nationality: string;
        seatNumber?: string;
        mealPreference?: string;
      }>;
      price: {
        base: number;
        taxes: number;
        fees: number;
        total: number;
      };
    };
    tripType: 'one-way' | 'round-trip' | 'multi-city';
  };

  // Hotel booking details
  hotel?: {
    property: string;
    checkIn: string;
    checkOut: string;
    nights: number;
    rooms: Array<{
      type: string;
      count: number;
      guests: {
        adults: number;
        children: number;
      };
      rate: number;
      total: number;
    }>;
    guests: Array<{
      title: string;
      firstName: string;
      lastName: string;
      isMainGuest: boolean;
    }>;
    specialRequests: string[];
    price: {
      roomTotal: number;
      taxes: number;
      fees: number;
      total: number;
    };
  };

  // Payment details
  payment: {
    method: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer' | 'wallet';
    transactionId?: string;
    amount: number;
    currency: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    paidAt?: string;
  };

  // Contact details
  contact: {
    email: string;
    phone: string;
    emergencyContact?: {
      name: string;
      phone: string;
      relationship: string;
    };
  };

  // Pricing breakdown
  pricing: {
    subtotal: number;
    taxes: number;
    fees: number;
    discounts: number;
    total: number;
    currency: string;
  };

  // Cancellation details
  cancellation?: {
    reason: string;
    cancelledAt: string;
    refundAmount: number;
    cancellationFee: number;
    refundStatus: 'pending' | 'processed' | 'failed';
  };
}

interface BookingState {
  bookings: Booking[];
  selectedBooking: Booking | null;
  bookingHistory: Booking[];
  isLoading: boolean;
  isCreating: boolean;
  error: string | null;
  currentBookingStep: number;
  bookingData: {
    passengers?: any[];
    contactInfo?: any;
    paymentInfo?: any;
  };
}

const initialState: BookingState = {
  bookings: [],
  selectedBooking: null,
  bookingHistory: [],
  isLoading: false,
  isCreating: false,
  error: null,
  currentBookingStep: 1,
  bookingData: {},
};

// Async thunks
export const createFlightBooking = createAsyncThunk(
  'bookings/createFlightBooking',
  async (bookingData: any, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.createFlightBooking(bookingData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Flight booking failed');
    }
  }
);

export const createHotelBooking = createAsyncThunk(
  'bookings/createHotelBooking',
  async (bookingData: any, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.createHotelBooking(bookingData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Hotel booking failed');
    }
  }
);

export const getBookings = createAsyncThunk(
  'bookings/getBookings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.getBookings();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get bookings');
    }
  }
);

export const getBookingDetails = createAsyncThunk(
  'bookings/getBookingDetails',
  async (bookingId: string, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.getBookingDetails(bookingId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get booking details');
    }
  }
);

export const cancelBooking = createAsyncThunk(
  'bookings/cancelBooking',
  async ({ bookingId, reason }: { bookingId: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.cancelBooking(bookingId, reason);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to cancel booking');
    }
  }
);

export const processPayment = createAsyncThunk(
  'bookings/processPayment',
  async ({ bookingId, paymentData }: { bookingId: string; paymentData: any }, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.processPayment(bookingId, paymentData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Payment processing failed');
    }
  }
);

const bookingSlice = createSlice({
  name: 'bookings',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedBooking: (state, action: PayloadAction<Booking | null>) => {
      state.selectedBooking = action.payload;
    },
    setCurrentBookingStep: (state, action: PayloadAction<number>) => {
      state.currentBookingStep = action.payload;
    },
    updateBookingData: (state, action: PayloadAction<Partial<BookingState['bookingData']>>) => {
      state.bookingData = { ...state.bookingData, ...action.payload };
    },
    clearBookingData: (state) => {
      state.bookingData = {};
      state.currentBookingStep = 1;
    },
    nextBookingStep: (state) => {
      state.currentBookingStep += 1;
    },
    previousBookingStep: (state) => {
      if (state.currentBookingStep > 1) {
        state.currentBookingStep -= 1;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Create flight booking
      .addCase(createFlightBooking.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createFlightBooking.fulfilled, (state, action) => {
        state.isCreating = false;
        state.bookings.unshift(action.payload);
        state.selectedBooking = action.payload;
        state.error = null;
      })
      .addCase(createFlightBooking.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      // Create hotel booking
      .addCase(createHotelBooking.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createHotelBooking.fulfilled, (state, action) => {
        state.isCreating = false;
        state.bookings.unshift(action.payload);
        state.selectedBooking = action.payload;
        state.error = null;
      })
      .addCase(createHotelBooking.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      // Get bookings
      .addCase(getBookings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBookings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bookings = action.payload;
        state.error = null;
      })
      .addCase(getBookings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get booking details
      .addCase(getBookingDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBookingDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedBooking = action.payload;
        state.error = null;
      })
      .addCase(getBookingDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Cancel booking
      .addCase(cancelBooking.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(cancelBooking.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.bookings.findIndex(booking => booking.id === action.payload.id);
        if (index !== -1) {
          state.bookings[index] = action.payload;
        }
        if (state.selectedBooking?.id === action.payload.id) {
          state.selectedBooking = action.payload;
        }
        state.error = null;
      })
      .addCase(cancelBooking.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSelectedBooking,
  setCurrentBookingStep,
  updateBookingData,
  clearBookingData,
  nextBookingStep,
  previousBookingStep,
} = bookingSlice.actions;

export default bookingSlice.reducer;
