import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link,
  IconButton,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  Email,
  Phone,
  LocationOn,
  FlightTakeoff,
  Hotel,
  Support,
} from '@mui/icons-material';

const Footer: React.FC = () => {
  const theme = useTheme();

  const footerLinks = {
    company: [
      { label: 'About Us', href: '/about' },
      { label: 'Careers', href: '/careers' },
      { label: 'Press', href: '/press' },
      { label: 'Blog', href: '/blog' },
    ],
    services: [
      { label: 'Flight Booking', href: '/flights' },
      { label: 'Hotel Booking', href: '/hotels' },
      { label: 'Travel Packages', href: '/packages' },
      { label: 'Travel Insurance', href: '/insurance' },
    ],
    support: [
      { label: 'Help Center', href: '/help' },
      { label: 'Contact Us', href: '/contact' },
      { label: 'Booking Management', href: '/manage-booking' },
      { label: 'Cancellation Policy', href: '/cancellation' },
    ],
    legal: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Cookie Policy', href: '/cookies' },
      { label: 'Refund Policy', href: '/refund' },
    ],
  };

  const socialLinks = [
    { icon: <Facebook />, href: 'https://facebook.com', label: 'Facebook' },
    { icon: <Twitter />, href: 'https://twitter.com', label: 'Twitter' },
    { icon: <Instagram />, href: 'https://instagram.com', label: 'Instagram' },
    { icon: <LinkedIn />, href: 'https://linkedin.com', label: 'LinkedIn' },
  ];

  const contactInfo = [
    { icon: <Email />, text: '<EMAIL>', href: 'mailto:<EMAIL>' },
    { icon: <Phone />, text: '+****************', href: 'tel:+15551234567' },
    { icon: <LocationOn />, text: '123 Travel Street, City, State 12345', href: '#' },
  ];

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: theme.palette.grey[900],
        color: 'white',
        py: 6,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Company Info */}
          <Grid item xs={12} md={3}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
              AI Travel Booking
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: 'grey.300' }}>
              Your intelligent travel companion. Book flights, hotels, and packages with AI-powered recommendations and smart pricing.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {socialLinks.map((social) => (
                <IconButton
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: 'grey.300',
                    '&:hover': {
                      color: 'primary.main',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                  aria-label={social.label}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>
          </Grid>

          {/* Company Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
              Company
            </Typography>
            {footerLinks.company.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                color="grey.300"
                underline="none"
                sx={{
                  display: 'block',
                  mb: 1,
                  '&:hover': {
                    color: 'primary.main',
                  },
                  transition: 'color 0.3s ease',
                }}
              >
                {link.label}
              </Link>
            ))}
          </Grid>

          {/* Services Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
              Services
            </Typography>
            {footerLinks.services.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                color="grey.300"
                underline="none"
                sx={{
                  display: 'block',
                  mb: 1,
                  '&:hover': {
                    color: 'primary.main',
                  },
                  transition: 'color 0.3s ease',
                }}
              >
                {link.label}
              </Link>
            ))}
          </Grid>

          {/* Support Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
              Support
            </Typography>
            {footerLinks.support.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                color="grey.300"
                underline="none"
                sx={{
                  display: 'block',
                  mb: 1,
                  '&:hover': {
                    color: 'primary.main',
                  },
                  transition: 'color 0.3s ease',
                }}
              >
                {link.label}
              </Link>
            ))}
          </Grid>

          {/* Contact Info */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
              Contact Us
            </Typography>
            {contactInfo.map((contact, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Box sx={{ mr: 1, color: 'primary.main' }}>
                  {contact.icon}
                </Box>
                <Link
                  href={contact.href}
                  color="grey.300"
                  underline="none"
                  sx={{
                    '&:hover': {
                      color: 'primary.main',
                    },
                    transition: 'color 0.3s ease',
                  }}
                >
                  <Typography variant="body2">{contact.text}</Typography>
                </Link>
              </Box>
            ))}
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'grey.700' }} />

        {/* Bottom Section */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="grey.400">
              © {new Date().getFullYear()} AI Travel Booking. All rights reserved.
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' }, gap: 3 }}>
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  color="grey.400"
                  underline="none"
                  variant="body2"
                  sx={{
                    '&:hover': {
                      color: 'primary.main',
                    },
                    transition: 'color 0.3s ease',
                  }}
                >
                  {link.label}
                </Link>
              ))}
            </Box>
          </Grid>
        </Grid>

        {/* Features Highlight */}
        <Box sx={{ mt: 4, pt: 4, borderTop: 1, borderColor: 'grey.700' }}>
          <Grid container spacing={3} justifyContent="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: 'center' }}>
              <FlightTakeoff sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Smart Flight Search
              </Typography>
              <Typography variant="body2" color="grey.400">
                AI-powered flight recommendations with price prediction
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4} sx={{ textAlign: 'center' }}>
              <Hotel sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Best Hotel Deals
              </Typography>
              <Typography variant="body2" color="grey.400">
                Curated hotel selections with personalized recommendations
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4} sx={{ textAlign: 'center' }}>
              <Support sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                24/7 AI Support
              </Typography>
              <Typography variant="body2" color="grey.400">
                Intelligent chatbot assistance for all your travel needs
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
