import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { hotelAPI } from '../../services/api';

export interface Hotel {
  id: string;
  name: string;
  description: string;
  address: {
    street: string;
    city: string;
    state?: string;
    country: string;
    zipCode?: string;
  };
  starRating: number;
  images: Array<{
    url: string;
    caption?: string;
    isPrimary: boolean;
  }>;
  amenities: {
    general: string[];
    room: string[];
    business: string[];
    recreation: string[];
  };
  rooms: Array<{
    type: string;
    name: string;
    description: string;
    maxOccupancy: {
      adults: number;
      children: number;
      total: number;
    };
    pricing: {
      baseRate: number;
      currency: string;
      taxesAndFees: number;
      totalRate: number;
    };
    amenities: string[];
    images: string[];
  }>;
  reviews: {
    overall: {
      rating: number;
      count: number;
    };
    categories: {
      cleanliness: number;
      service: number;
      location: number;
      value: number;
      amenities: number;
    };
  };
  pricing: {
    currency: string;
    priceRange: {
      min: number;
      max: number;
    };
    averageNightlyRate: number;
  };
  location: {
    nearbyAttractions: Array<{
      name: string;
      distance: number;
      unit: string;
      category: string;
    }>;
  };
}

export interface HotelSearchParams {
  city: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
  minPrice?: number;
  maxPrice?: number;
  starRating?: number;
  amenities?: string[];
}

interface HotelState {
  hotels: Hotel[];
  selectedHotel: Hotel | null;
  searchParams: HotelSearchParams | null;
  searchResults: {
    hotels: Hotel[];
    total: number;
    page: number;
    totalPages: number;
  };
  popularHotels: Hotel[];
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  filters: {
    priceRange: [number, number];
    starRating: number[];
    amenities: string[];
    guestRating: number;
    propertyType: string[];
  };
  sortBy: 'price' | 'rating' | 'distance' | 'popularity';
  sortOrder: 'asc' | 'desc';
}

const initialState: HotelState = {
  hotels: [],
  selectedHotel: null,
  searchParams: null,
  searchResults: {
    hotels: [],
    total: 0,
    page: 1,
    totalPages: 0,
  },
  popularHotels: [],
  isLoading: false,
  isSearching: false,
  error: null,
  filters: {
    priceRange: [0, 1000],
    starRating: [],
    amenities: [],
    guestRating: 0,
    propertyType: [],
  },
  sortBy: 'price',
  sortOrder: 'asc',
};

// Async thunks
export const searchHotels = createAsyncThunk(
  'hotels/searchHotels',
  async (searchParams: HotelSearchParams & { page?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await hotelAPI.searchHotels(searchParams);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Hotel search failed');
    }
  }
);

export const getHotelDetails = createAsyncThunk(
  'hotels/getHotelDetails',
  async (hotelId: string, { rejectWithValue }) => {
    try {
      const response = await hotelAPI.getHotelDetails(hotelId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get hotel details');
    }
  }
);

export const getPopularHotels = createAsyncThunk(
  'hotels/getPopularHotels',
  async (_, { rejectWithValue }) => {
    try {
      const response = await hotelAPI.getPopularHotels();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get popular hotels');
    }
  }
);

const hotelSlice = createSlice({
  name: 'hotels',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchParams: (state, action: PayloadAction<HotelSearchParams>) => {
      state.searchParams = action.payload;
    },
    setSelectedHotel: (state, action: PayloadAction<Hotel | null>) => {
      state.selectedHotel = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<HotelState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSorting: (state, action: PayloadAction<{ sortBy: HotelState['sortBy']; sortOrder: HotelState['sortOrder'] }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearSearchResults: (state) => {
      state.searchResults = {
        hotels: [],
        total: 0,
        page: 1,
        totalPages: 0,
      };
      state.searchParams = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Search hotels
      .addCase(searchHotels.pending, (state) => {
        state.isSearching = true;
        state.error = null;
      })
      .addCase(searchHotels.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload;
        state.error = null;
      })
      .addCase(searchHotels.rejected, (state, action) => {
        state.isSearching = false;
        state.error = action.payload as string;
      })
      // Get hotel details
      .addCase(getHotelDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getHotelDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedHotel = action.payload;
        state.error = null;
      })
      .addCase(getHotelDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get popular hotels
      .addCase(getPopularHotels.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPopularHotels.fulfilled, (state, action) => {
        state.isLoading = false;
        state.popularHotels = action.payload;
        state.error = null;
      })
      .addCase(getPopularHotels.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSearchParams,
  setSelectedHotel,
  updateFilters,
  setSorting,
  clearSearchResults,
} = hotelSlice.actions;

export default hotelSlice.reducer;
