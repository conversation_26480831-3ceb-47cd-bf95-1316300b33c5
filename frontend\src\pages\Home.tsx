import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  FlightTakeoff,
  Hotel,
  TrendingUp,
  SmartToy,
  Security,
  Speed,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { setPageTitle } from '../store/slices/uiSlice';

const Home: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    dispatch(setPageTitle('Home'));
  }, [dispatch]);

  const features = [
    {
      icon: <SmartToy sx={{ fontSize: 40 }} />,
      title: 'AI-Powered Recommendations',
      description: 'Get personalized travel suggestions based on your preferences and travel history.',
    },
    {
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      title: 'Price Prediction',
      description: 'Smart algorithms predict the best time to book for maximum savings.',
    },
    {
      icon: <Speed sx={{ fontSize: 40 }} />,
      title: 'Instant Booking',
      description: 'Book flights and hotels in seconds with our streamlined process.',
    },
    {
      icon: <Security sx={{ fontSize: 40 }} />,
      title: 'Secure Payments',
      description: 'Your transactions are protected with bank-level security.',
    },
  ];

  const popularDestinations = [
    {
      city: 'Paris',
      country: 'France',
      image: 'https://images.unsplash.com/photo-*************-47ad22581b52?w=400',
      price: 'From $599',
      description: 'City of Light',
    },
    {
      city: 'Tokyo',
      country: 'Japan',
      image: 'https://images.unsplash.com/photo-*************-eab4deabeeaf?w=400',
      price: 'From $899',
      description: 'Modern Metropolis',
    },
    {
      city: 'New York',
      country: 'USA',
      image: 'https://images.unsplash.com/photo-*************-8d4d0e62e6e9?w=400',
      price: 'From $399',
      description: 'The Big Apple',
    },
    {
      city: 'London',
      country: 'UK',
      image: 'https://images.unsplash.com/photo-*************-59663e0ac1ad?w=400',
      price: 'From $549',
      description: 'Historic Capital',
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)}, ${alpha(theme.palette.secondary.main, 0.9)}), url('https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=1200') center/cover`,
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 'bold',
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              mb: 2,
            }}
          >
            Your AI-Powered Travel Companion
          </Typography>
          <Typography
            variant="h5"
            sx={{
              mb: 4,
              opacity: 0.9,
              fontSize: { xs: '1.2rem', md: '1.5rem' },
              maxWidth: '800px',
              mx: 'auto',
            }}
          >
            Discover amazing destinations, get personalized recommendations, and book your perfect trip with intelligent pricing insights.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<FlightTakeoff />}
              onClick={() => navigate('/flights')}
              sx={{
                backgroundColor: 'white',
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.9),
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
                px: 4,
                py: 1.5,
              }}
            >
              Search Flights
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Hotel />}
              onClick={() => navigate('/hotels')}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.1),
                  borderColor: 'white',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
                px: 4,
                py: 1.5,
              }}
            >
              Find Hotels
            </Button>
          </Box>
          {isAuthenticated && (
            <Typography variant="body1" sx={{ mt: 3, opacity: 0.8 }}>
              Welcome back, {user?.firstName}! Ready for your next adventure?
            </Typography>
          )}
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h3"
          component="h2"
          textAlign="center"
          gutterBottom
          sx={{ mb: 6, fontWeight: 'bold' }}
        >
          Why Choose AI Travel Booking?
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: theme.shadows[8],
                  },
                }}
              >
                <Box sx={{ color: 'primary.main', mb: 2 }}>
                  {feature.icon}
                </Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                  {feature.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {feature.description}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Popular Destinations */}
      <Box sx={{ backgroundColor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            textAlign="center"
            gutterBottom
            sx={{ mb: 6, fontWeight: 'bold' }}
          >
            Popular Destinations
          </Typography>
          <Grid container spacing={4}>
            {popularDestinations.map((destination, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8],
                    },
                  }}
                  onClick={() => navigate(`/flights?to=${destination.city}`)}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={destination.image}
                    alt={destination.city}
                  />
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" component="h3" sx={{ fontWeight: 'bold' }}>
                        {destination.city}
                      </Typography>
                      <Chip
                        label={destination.price}
                        color="primary"
                        size="small"
                        sx={{ fontWeight: 'bold' }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {destination.country}
                    </Typography>
                    <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                      {destination.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/flights')}
              sx={{ px: 4 }}
            >
              Explore All Destinations
            </Button>
          </Box>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
            Ready to Start Your Journey?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Join thousands of travelers who trust AI Travel Booking for their adventures.
          </Typography>
          {!isAuthenticated ? (
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/register')}
                sx={{
                  backgroundColor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    backgroundColor: alpha('#fff', 0.9),
                  },
                  px: 4,
                }}
              >
                Sign Up Now
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/login')}
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: alpha('#fff', 0.1),
                  },
                  px: 4,
                }}
              >
                Login
              </Button>
            </Box>
          ) : (
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/flights')}
              sx={{
                backgroundColor: 'white',
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: alpha('#fff', 0.9),
                },
                px: 4,
              }}
            >
              Start Planning Your Trip
            </Button>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
